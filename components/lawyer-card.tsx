"use client"

import type React from "react"

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Star, Phone, Globe, MapPin, Clock, Award } from "lucide-react"
import Image from "next/image"
import Link from "next/link"

interface LawyerCardProps {
  lawyer: {
    source_id: string
    title: string
    category_name: string
    address: string
    city: string
    state: string
    phone: string
    website: string
    total_score: number
    reviews_count: number
    opening_hours: Array<{ day: string; hours: string }>
    image_url: string
    processed_reviews: {
      seo_snippets: Array<{ text: string }>
      service_keywords: string[]
    }
  }
}

export function LawyerCard({ lawyer }: LawyerCardProps) {
  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star
        key={i}
        className={`h-4 w-4 ${i < Math.floor(rating) ? "fill-yellow-400 text-yellow-400" : "text-gray-300"}`}
      />
    ))
  }

  const isOpen24Hours = lawyer.opening_hours.every((day) => day.hours === "Open 24 hours")

  const handlePhoneClick = (e: React.MouseEvent) => {
    e.stopPropagation()
    window.location.href = `tel:${lawyer.phone}`
  }

  const handleWebsiteClick = (e: React.MouseEvent) => {
    e.stopPropagation()
    window.open(lawyer.website, "_blank", "noopener,noreferrer")
  }

  return (
    <Link href={`/lawyer/${lawyer.source_id}`} className="block h-full">
      <Card className="h-full flex flex-col hover:shadow-lg transition-shadow duration-200 cursor-pointer">
        <CardHeader className="pb-4">
          <div className="relative h-48 w-full mb-4 rounded-lg overflow-hidden">
            <Image src={lawyer.image_url || "/placeholder.svg"} alt={lawyer.title} fill className="object-cover" />
            <div className="absolute top-2 right-2">
              <Badge variant="secondary" className="bg-primary text-primary-foreground">
                <Award className="h-3 w-3 mr-1" />
                Top Rated
              </Badge>
            </div>
          </div>

          <div className="space-y-2">
            <h3 className="font-bold text-lg leading-tight line-clamp-2">{lawyer.title}</h3>

            <div className="flex items-center space-x-2">
              <div className="flex items-center">{renderStars(lawyer.total_score)}</div>
              <span className="font-semibold">{lawyer.total_score}</span>
              <span className="text-muted-foreground">({lawyer.reviews_count} reviews)</span>
            </div>

            <div className="flex items-center text-muted-foreground">
              <MapPin className="h-4 w-4 mr-1" />
              <span className="text-sm">
                {lawyer.city}, {lawyer.state}
              </span>
            </div>

            {isOpen24Hours && (
              <div className="flex items-center text-green-600">
                <Clock className="h-4 w-4 mr-1" />
                <span className="text-sm font-medium">Available 24/7</span>
              </div>
            )}
          </div>
        </CardHeader>

        <CardContent className="flex-1">
          <p className="text-sm text-muted-foreground mb-4 line-clamp-3">
            {lawyer.processed_reviews.seo_snippets[0]?.text}
          </p>

          <div className="flex flex-wrap gap-2">
            {lawyer.processed_reviews.service_keywords.slice(0, 3).map((keyword, index) => (
              <Badge key={index} variant="outline" className="text-xs">
                {keyword}
              </Badge>
            ))}
          </div>
        </CardContent>

        <CardFooter className="pt-4 space-y-2">
          <div className="flex space-x-2 w-full">
            <Button variant="outline" size="sm" className="flex-1 bg-transparent" onClick={handlePhoneClick}>
              <Phone className="h-4 w-4 mr-1" />
              Call Now
            </Button>

            <Button variant="outline" size="sm" className="flex-1 bg-transparent" onClick={handleWebsiteClick}>
              <Globe className="h-4 w-4 mr-1" />
              Website
            </Button>
          </div>
        </CardFooter>
      </Card>
    </Link>
  )
}
