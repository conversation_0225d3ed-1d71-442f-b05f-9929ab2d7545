"use client"

import { useState } from "react"
import { LawyerCard } from "@/components/lawyer-card"
import { Button } from "@/components/ui/button"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"

// Sample data based on the provided JSON structure
const lawyersData = [
  {
    source_id: "04e46dc5-af24-4204-b8bf-75bbf0328a1c",
    title: "<PERSON> Car & Truck Accident Lawyers - Fort Collins, Colorado",
    category_name: "Personal injury attorney",
    address: "1730 S College Ave Unit 202, Fort Collins, CO 80525",
    city: "Fort Collins",
    state: "Colorado",
    phone: "(*************",
    website: "https://www.fdazar.com/fort-collins-car-accident-attorney/",
    total_score: 4.9,
    reviews_count: 266,
    opening_hours: [
      { day: "Monday", hours: "Open 24 hours" },
      { day: "Tuesday", hours: "Open 24 hours" },
      { day: "Wednesday", hours: "Open 24 hours" },
      { day: "Thursday", hours: "Open 24 hours" },
      { day: "Friday", hours: "Open 24 hours" },
      { day: "Saturday", hours: "Open 24 hours" },
      { day: "Sunday", hours: "Open 24 hours" },
    ],
    image_url: "/professional-law-office-exterior.png",
    processed_reviews: {
      seo_snippets: [
        {
          text: "Experience exceptional legal support with top-tier attorneys. Our firm offers compassionate, knowledgeable, and friendly service, prioritizing your needs from start to finish for successful settlements.",
        },
      ],
      service_keywords: ["car accident", "personal injury", "settlement", "legal representation"],
    },
  },
  {
    source_id: "fb415281-e98f-4fac-91e3-531e28726a35",
    title: "Workman Car Accident & Personal Injury Lawyers Fort Lauderdale",
    category_name: "Personal injury attorney",
    address: "633 S Andrews Ave Ste 401, Fort Lauderdale, FL 33301",
    city: "Fort Lauderdale",
    state: "Florida",
    phone: "(*************",
    website: "https://workmaninjurylaw.com/",
    total_score: 5.0,
    reviews_count: 265,
    opening_hours: [
      { day: "Monday", hours: "Open 24 hours" },
      { day: "Tuesday", hours: "Open 24 hours" },
      { day: "Wednesday", hours: "Open 24 hours" },
      { day: "Thursday", hours: "Open 24 hours" },
      { day: "Friday", hours: "Open 24 hours" },
      { day: "Saturday", hours: "Open 24 hours" },
      { day: "Sunday", hours: "Open 24 hours" },
    ],
    image_url: "/modern-law-firm-office-interior.png",
    processed_reviews: {
      seo_snippets: [
        {
          text: "Clients consistently praise the exceptional dedication and expertise of the legal team, particularly highlighting their ability to navigate complex cases with unwavering support.",
        },
      ],
      service_keywords: ["motor vehicle accident", "personal injury", "legal representation", "compensation"],
    },
  },
  {
    source_id: "a13424b9-b292-4fb8-9413-af7e85446caf",
    title: "Jeffrey Glassman Injury Lawyers",
    category_name: "Personal injury attorney",
    address: "1 International Pl #1810, Boston, MA 02110",
    city: "Boston",
    state: "Massachusetts",
    phone: "(*************",
    website: "https://www.jeffreysglassman.com/",
    total_score: 4.9,
    reviews_count: 628,
    opening_hours: [
      { day: "Monday", hours: "Open 24 hours" },
      { day: "Tuesday", hours: "Open 24 hours" },
      { day: "Wednesday", hours: "Open 24 hours" },
      { day: "Thursday", hours: "Open 24 hours" },
      { day: "Friday", hours: "Open 24 hours" },
      { day: "Saturday", hours: "Open 24 hours" },
      { day: "Sunday", hours: "Open 24 hours" },
    ],
    image_url: "/attorney-headshot.png",
    processed_reviews: {
      seo_snippets: [
        {
          text: "Experience exceptional legal support with Jeffrey Glassman Injury Law. Attorneys provide compassionate, diligent, and trustworthy guidance throughout your injury claim.",
        },
      ],
      service_keywords: ["auto accident", "personal injury", "legal consultation", "compensation"],
    },
  },
]

export function LawyerDirectory() {
  const [sortBy, setSortBy] = useState("rating")
  const [filterBy, setFilterBy] = useState("all")

  const filteredLawyers = lawyersData.filter((lawyer) => {
    if (filterBy === "all") return true
    return lawyer.state.toLowerCase() === filterBy.toLowerCase()
  })

  const sortedLawyers = [...filteredLawyers].sort((a, b) => {
    switch (sortBy) {
      case "rating":
        return b.total_score - a.total_score
      case "reviews":
        return b.reviews_count - a.reviews_count
      case "name":
        return a.title.localeCompare(b.title)
      default:
        return 0
    }
  })

  return (
    <section className="py-16 bg-muted/30">
      <div className="container mx-auto px-4">
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-8">
          <div>
            <h2 className="text-3xl font-bold mb-2">Top Car Accident Lawyers</h2>
            <p className="text-muted-foreground">{filteredLawyers.length} lawyers found</p>
          </div>

          <div className="flex flex-col sm:flex-row gap-4 mt-4 md:mt-0">
            <Select value={sortBy} onValueChange={setSortBy}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Sort by" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="rating">Highest Rated</SelectItem>
                <SelectItem value="reviews">Most Reviews</SelectItem>
                <SelectItem value="name">Name A-Z</SelectItem>
              </SelectContent>
            </Select>

            <Select value={filterBy} onValueChange={setFilterBy}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Filter by state" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All States</SelectItem>
                <SelectItem value="colorado">Colorado</SelectItem>
                <SelectItem value="florida">Florida</SelectItem>
                <SelectItem value="massachusetts">Massachusetts</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {sortedLawyers.map((lawyer) => (
            <LawyerCard key={lawyer.source_id} lawyer={lawyer} />
          ))}
        </div>

        {sortedLawyers.length === 0 && (
          <div className="text-center py-12">
            <p className="text-muted-foreground text-lg">No lawyers found matching your criteria.</p>
            <Button variant="outline" className="mt-4 bg-transparent" onClick={() => setFilterBy("all")}>
              Clear Filters
            </Button>
          </div>
        )}
      </div>
    </section>
  )
}
