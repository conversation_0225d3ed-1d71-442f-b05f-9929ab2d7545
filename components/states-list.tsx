import Link from 'next/link'
import { generateStateSlug } from "@/lib/utils/seo"

interface StatesListProps {
  states: string[]
}

export function StatesList({ states }: StatesListProps) {
  if (!states || states.length === 0) {
    return null
  }

  // Sort states alphabetically
  const sortedStates = [...states].sort()

  return (
    <section className="py-16 bg-background">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold mb-4">Find Car Accident Attorneys by State</h2>
          <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
            Browse our directory of experienced car accident attorneys organized by state. Find qualified legal representation in your area.
          </p>
        </div>

        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4">
          {sortedStates.map((state) => (
            <Link
              key={state}
              href={`/${generateStateSlug(state)}`}
              className="block p-4 bg-card rounded-lg border hover:shadow-md transition-shadow duration-200 text-center"
            >
              <span className="font-medium text-foreground hover:text-primary transition-colors">
                {state}
              </span>
            </Link>
          ))}
        </div>
      </div>
    </section>
  )
}
