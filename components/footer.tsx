import { Scale, Phone, Mail, MapPin } from "lucide-react"

export function Footer() {
  return (
    <footer className="bg-primary text-primary-foreground">
      <div className="container mx-auto px-4 py-12">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          <div className="space-y-4">
            <div className="flex items-center space-x-2">
              <Scale className="h-6 w-6" />
              <span className="text-lg font-bold">LegalFind</span>
            </div>
            <p className="text-primary-foreground/80 text-sm">
              Connecting accident victims with trusted legal representation across the United States.
            </p>
          </div>

          <div>
            <h4 className="font-semibold mb-4">Quick Links</h4>
            <ul className="space-y-2 text-sm">
              <li>
                <a href="#" className="hover:text-accent transition-colors">
                  Find Lawyers
                </a>
              </li>
              <li>
                <a href="#" className="hover:text-accent transition-colors">
                  Practice Areas
                </a>
              </li>
              <li>
                <a href="#" className="hover:text-accent transition-colors">
                  Legal Resources
                </a>
              </li>
              <li>
                <a href="#" className="hover:text-accent transition-colors">
                  About Us
                </a>
              </li>
            </ul>
          </div>

          <div>
            <h4 className="font-semibold mb-4">Legal Areas</h4>
            <ul className="space-y-2 text-sm">
              <li>
                <a href="#" className="hover:text-accent transition-colors">
                  Car Accidents
                </a>
              </li>
              <li>
                <a href="#" className="hover:text-accent transition-colors">
                  Truck Accidents
                </a>
              </li>
              <li>
                <a href="#" className="hover:text-accent transition-colors">
                  Motorcycle Accidents
                </a>
              </li>
              <li>
                <a href="#" className="hover:text-accent transition-colors">
                  Personal Injury
                </a>
              </li>
            </ul>
          </div>

          <div>
            <h4 className="font-semibold mb-4">Contact</h4>
            <div className="space-y-2 text-sm">
              <div className="flex items-center space-x-2">
                <Phone className="h-4 w-4" />
                <span>1-800-LEGAL-HELP</span>
              </div>
              <div className="flex items-center space-x-2">
                <Mail className="h-4 w-4" />
                <span><EMAIL></span>
              </div>
              <div className="flex items-center space-x-2">
                <MapPin className="h-4 w-4" />
                <span>Nationwide Service</span>
              </div>
            </div>
          </div>
        </div>

        <div className="border-t border-primary-foreground/20 mt-8 pt-8 text-center text-sm text-primary-foreground/80">
          <p>&copy; 2025 LegalFind. All rights reserved. | Privacy Policy | Terms of Service</p>
        </div>
      </div>
    </footer>
  )
}
