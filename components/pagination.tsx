import Link from 'next/link'
import { ChevronLeft, ChevronRight } from 'lucide-react'
import { Button } from "@/components/ui/button"
import { generatePageNumbers } from "@/lib/utils/pagination"

interface PaginationComponentProps {
  currentPage: number
  totalPages: number
  baseUrl: string
}

export function PaginationComponent({ currentPage, totalPages, baseUrl }: PaginationComponentProps) {
  if (totalPages <= 1) return null

  const pageNumbers = generatePageNumbers(currentPage, totalPages)
  const hasPrevious = currentPage > 1
  const hasNext = currentPage < totalPages

  const getPageUrl = (page: number) => {
    if (page === 1) return baseUrl
    return `${baseUrl}?page=${page}`
  }

  return (
    <nav className="flex items-center justify-center space-x-2" aria-label="Pagination">
      {/* Previous button */}
      {hasPrevious ? (
        <Link href={getPageUrl(currentPage - 1)}>
          <Button variant="outline" size="sm" className="flex items-center">
            <ChevronLeft className="h-4 w-4 mr-1" />
            Previous
          </Button>
        </Link>
      ) : (
        <Button variant="outline" size="sm" disabled className="flex items-center">
          <ChevronLeft className="h-4 w-4 mr-1" />
          Previous
        </Button>
      )}

      {/* Page numbers */}
      <div className="flex items-center space-x-1">
        {/* First page if not in visible range */}
        {pageNumbers[0] > 1 && (
          <>
            <Link href={getPageUrl(1)}>
              <Button variant={1 === currentPage ? "default" : "outline"} size="sm">
                1
              </Button>
            </Link>
            {pageNumbers[0] > 2 && (
              <span className="px-2 text-muted-foreground">...</span>
            )}
          </>
        )}

        {/* Visible page numbers */}
        {pageNumbers.map((page) => (
          <Link key={page} href={getPageUrl(page)}>
            <Button 
              variant={page === currentPage ? "default" : "outline"} 
              size="sm"
              aria-current={page === currentPage ? "page" : undefined}
            >
              {page}
            </Button>
          </Link>
        ))}

        {/* Last page if not in visible range */}
        {pageNumbers[pageNumbers.length - 1] < totalPages && (
          <>
            {pageNumbers[pageNumbers.length - 1] < totalPages - 1 && (
              <span className="px-2 text-muted-foreground">...</span>
            )}
            <Link href={getPageUrl(totalPages)}>
              <Button variant={totalPages === currentPage ? "default" : "outline"} size="sm">
                {totalPages}
              </Button>
            </Link>
          </>
        )}
      </div>

      {/* Next button */}
      {hasNext ? (
        <Link href={getPageUrl(currentPage + 1)}>
          <Button variant="outline" size="sm" className="flex items-center">
            Next
            <ChevronRight className="h-4 w-4 ml-1" />
          </Button>
        </Link>
      ) : (
        <Button variant="outline" size="sm" disabled className="flex items-center">
          Next
          <ChevronRight className="h-4 w-4 ml-1" />
        </Button>
      )}
    </nav>
  )
}
