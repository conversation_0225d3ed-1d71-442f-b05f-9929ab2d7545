import { But<PERSON> } from "@/components/ui/button"
import { Scale } from "lucide-react"

export function Header() {
  return (
    <header className="bg-primary text-primary-foreground">
      <div className="container mx-auto px-4 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Scale className="h-8 w-8" />
            <span className="text-xl font-bold">LegalFind</span>
          </div>

          <nav className="hidden md:flex items-center space-x-6">
            <a href="#" className="hover:text-accent transition-colors">
              Home
            </a>
            <a href="#" className="hover:text-accent transition-colors">
              Lawyers
            </a>
            <a href="#" className="hover:text-accent transition-colors">
              About
            </a>
            <a href="#" className="hover:text-accent transition-colors">
              Contact
            </a>
          </nav>

          <div className="flex items-center space-x-2">
            <Button variant="secondary" size="sm">
              Sign In
            </Button>
            <Button
              variant="outline"
              size="sm"
              className="border-primary-foreground text-primary-foreground hover:bg-primary-foreground hover:text-primary bg-transparent"
            >
              Register
            </Button>
          </div>
        </div>
      </div>
    </header>
  )
}
