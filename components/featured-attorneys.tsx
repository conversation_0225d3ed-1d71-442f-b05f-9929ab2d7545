import Link from 'next/link'
import { <PERSON>, Card<PERSON>ontent, CardHeader } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Star, MapPin, Phone } from "lucide-react"
import { Attorney } from "@/lib/supabase"
import { generateStateSlug, generateCitySlug } from "@/lib/utils/seo"

interface FeaturedAttorneysProps {
  attorneys: Attorney[]
}

export function FeaturedAttorneys({ attorneys }: FeaturedAttorneysProps) {
  if (!attorneys || attorneys.length === 0) {
    return null
  }

  return (
    <section className="py-16 bg-muted/50">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold mb-4">Featured Car Accident Attorneys</h2>
          <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
            Connect with top-rated car accident attorneys in your area. These lawyers have been highly rated by their clients for their expertise and results.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {attorneys.map((attorney) => (
            <Link
              key={attorney.source_id}
              href={`/${generateStateSlug(attorney.state)}/${generateCitySlug(attorney.city)}/${attorney.source_id}`}
              className="block h-full"
            >
              <Card className="h-full hover:shadow-lg transition-shadow duration-200 cursor-pointer">
                <CardHeader className="pb-4">
                  <div className="flex items-start justify-between mb-2">
                    <div className="flex items-center space-x-1">
                      <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                      <span className="font-semibold text-sm">
                        {attorney.total_score.toFixed(1)}
                      </span>
                      <span className="text-sm text-muted-foreground">
                        ({attorney.reviews_count} reviews)
                      </span>
                    </div>
                  </div>
                  
                  <h3 className="font-semibold text-lg leading-tight line-clamp-2 mb-2">
                    {attorney.title}
                  </h3>
                  
                  <div className="flex items-center text-sm text-muted-foreground mb-2">
                    <MapPin className="h-4 w-4 mr-1 flex-shrink-0" />
                    <span className="line-clamp-1">
                      {attorney.city}, {attorney.state}
                    </span>
                  </div>

                  {attorney.phone && (
                    <div className="flex items-center text-sm text-muted-foreground mb-3">
                      <Phone className="h-4 w-4 mr-1 flex-shrink-0" />
                      <span>{attorney.phone}</span>
                    </div>
                  )}
                </CardHeader>

                <CardContent className="pt-0">
                  {attorney.seo_snippets && attorney.seo_snippets[0] && (
                    <p className="text-sm text-muted-foreground line-clamp-3 mb-4">
                      {attorney.seo_snippets[0].text.substring(0, 120)}...
                    </p>
                  )}

                  {attorney.service_keywords && attorney.service_keywords.length > 0 && (
                    <div className="flex flex-wrap gap-1">
                      {attorney.service_keywords.slice(0, 3).map((keyword, index) => (
                        <Badge key={index} variant="secondary" className="text-xs">
                          {keyword}
                        </Badge>
                      ))}
                      {attorney.service_keywords.length > 3 && (
                        <Badge variant="outline" className="text-xs">
                          +{attorney.service_keywords.length - 3} more
                        </Badge>
                      )}
                    </div>
                  )}
                </CardContent>
              </Card>
            </Link>
          ))}
        </div>

        <div className="text-center mt-12">
          <Link
            href="/search"
            className="inline-flex items-center px-6 py-3 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-colors font-medium"
          >
            View All Attorneys
          </Link>
        </div>
      </div>
    </section>
  )
}
