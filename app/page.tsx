import { Suspense } from 'react'
import { <PERSON><PERSON> } from "@/components/header"
import { HeroSection } from "@/components/hero-section"
import { LawyerDirectory } from "@/components/lawyer-directory"
import { Footer } from "@/components/footer"
import { FeaturedAttorneys } from "@/components/featured-attorneys"
import { StatesList } from "@/components/states-list"
import { SchemaMarkup } from "@/components/schema-markup"
import { getAttorneys, getStates } from "@/lib/supabase"

export default async function HomePage() {
  // Get featured attorneys (top-rated)
  const { attorneys: featuredAttorneys } = await getAttorneys(1, 12)
  const { states } = await getStates()

  const homePageSchema = {
    '@context': 'https://schema.org',
    '@type': 'WebSite',
    name: 'Find Car Accident Attorneys',
    url: 'https://findcaraccidentattorneys.org',
    description: 'Find the best car accident attorneys in your area. Browse verified reviews, ratings, and contact information for top-rated personal injury lawyers.',
    potentialAction: {
      '@type': 'SearchAction',
      target: {
        '@type': 'EntryPoint',
        urlTemplate: 'https://findcaraccidentattorneys.org/search?q={search_term_string}'
      },
      'query-input': 'required name=search_term_string'
    },
    publisher: {
      '@type': 'Organization',
      name: 'Find Car Accident Attorneys',
      url: 'https://findcaraccidentattorneys.org'
    }
  }

  return (
    <div className="min-h-screen bg-background">
      <SchemaMarkup data={homePageSchema} />
      <Header />
      <HeroSection />

      <Suspense fallback={<div className="container mx-auto px-4 py-8">Loading featured attorneys...</div>}>
        <FeaturedAttorneys attorneys={featuredAttorneys} />
      </Suspense>

      <LawyerDirectory />

      <Suspense fallback={<div className="container mx-auto px-4 py-8">Loading states...</div>}>
        <StatesList states={states} />
      </Suspense>

      <Footer />
    </div>
  )
}
