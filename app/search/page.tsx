"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/header"
import { Footer } from "@/components/footer"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { Star, MapPin, Clock, Heart } from "lucide-react"
import Image from "next/image"
import Link from "next/link"

// Sample data based on the provided JSON structure
const lawyersData = [
  {
    source_id: "04e46dc5-af24-4204-b8bf-75bbf0328a1c",
    title: "<PERSON> Car & Truck Accident Lawyers",
    category_name: "Personal injury attorney",
    address: "1730 S College Ave Unit 202, Fort Collins, CO 80525",
    city: "Fort Collins",
    state: "Colorado",
    phone: "(*************",
    website: "https://www.fdazar.com/fort-collins-car-accident-attorney/",
    total_score: 4.9,
    reviews_count: 266,
    credentials: "J<PERSON>, Personal Injury Specialist",
    availability: "Available Today",
    pricing: "Starting From $296.00",
    opening_hours: [
      { day: "Monday", hours: "Open 24 hours" },
      { day: "Tuesday", hours: "Open 24 hours" },
      { day: "Wednesday", hours: "Open 24 hours" },
      { day: "Thursday", hours: "Open 24 hours" },
      { day: "Friday", hours: "Open 24 hours" },
      { day: "Saturday", hours: "Open 24 hours" },
      { day: "Sunday", hours: "Open 24 hours" },
    ],
    image_url: "/professional-law-office-exterior.png",
    processed_reviews: {
      seo_snippets: [
        {
          text: "Experience exceptional legal support with top-tier attorneys. Our firm offers compassionate, knowledgeable, and friendly service.",
        },
      ],
      service_keywords: [
        "Car accident",
        "Personal injury",
        "Settlement",
        "Legal representation",
        "Insurance claims",
        "Compensation",
      ],
    },
  },
  {
    source_id: "fb415281-e98f-4fac-91e3-531e28726a35",
    title: "Workman Car Accident & Personal Injury Lawyers",
    category_name: "Personal injury attorney",
    address: "633 S Andrews Ave Ste 401, Fort Lauderdale, FL 33301",
    city: "Fort Lauderdale",
    state: "Florida",
    phone: "(*************",
    website: "https://workmaninjurylaw.com/",
    total_score: 5.0,
    reviews_count: 265,
    credentials: "JD, Trial Attorney",
    availability: "Available Today",
    pricing: "Starting From $219.00",
    opening_hours: [
      { day: "Monday", hours: "Open 24 hours" },
      { day: "Tuesday", hours: "Open 24 hours" },
      { day: "Wednesday", hours: "Open 24 hours" },
      { day: "Thursday", hours: "Open 24 hours" },
      { day: "Friday", hours: "Open 24 hours" },
      { day: "Saturday", hours: "Open 24 hours" },
      { day: "Sunday", hours: "Open 24 hours" },
    ],
    image_url: "/modern-law-firm-office-interior.png",
    processed_reviews: {
      seo_snippets: [
        {
          text: "Clients consistently praise the exceptional dedication and expertise of the legal team, particularly highlighting their ability to navigate complex cases.",
        },
      ],
      service_keywords: [
        "Motor vehicle accident",
        "Personal injury",
        "Legal representation",
        "Compensation",
        "Insurance disputes",
        "Trial advocacy",
      ],
    },
  },
  {
    source_id: "a13424b9-b292-4fb8-9413-af7e85446caf",
    title: "Jeffrey Glassman Injury Lawyers",
    category_name: "Personal injury attorney",
    address: "1 International Pl #1810, Boston, MA 02110",
    city: "Boston",
    state: "Massachusetts",
    phone: "(*************",
    website: "https://www.jeffreysglassman.com/",
    total_score: 4.8,
    reviews_count: 628,
    credentials: "JD, Personal Injury Law",
    availability: "Available Today",
    pricing: "Starting From $217.00",
    opening_hours: [
      { day: "Monday", hours: "Open 24 hours" },
      { day: "Tuesday", hours: "Open 24 hours" },
      { day: "Wednesday", hours: "Open 24 hours" },
      { day: "Thursday", hours: "Open 24 hours" },
      { day: "Friday", hours: "Open 24 hours" },
      { day: "Saturday", hours: "Open 24 hours" },
      { day: "Sunday", hours: "Open 24 hours" },
    ],
    image_url: "/attorney-headshot.png",
    processed_reviews: {
      seo_snippets: [
        {
          text: "Experience exceptional legal support with Jeffrey Glassman Injury Law. Attorneys provide compassionate, diligent, and trustworthy guidance.",
        },
      ],
      service_keywords: [
        "Auto accident",
        "Personal injury",
        "Legal consultation",
        "Compensation",
        "Medical malpractice",
        "Wrongful death",
      ],
    },
  },
]

export default function SearchPage() {
  const [sortBy, setSortBy] = useState("rating")
  const [orderBy, setOrderBy] = useState("desc")

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star
        key={i}
        className={`h-4 w-4 ${i < Math.floor(rating) ? "fill-yellow-400 text-yellow-400" : "text-gray-300"}`}
      />
    ))
  }

  const sortedLawyers = [...lawyersData].sort((a, b) => {
    const multiplier = orderBy === "desc" ? -1 : 1
    switch (sortBy) {
      case "rating":
        return (b.total_score - a.total_score) * multiplier
      case "reviews":
        return (b.reviews_count - a.reviews_count) * multiplier
      case "name":
        return a.title.localeCompare(b.title) * multiplier
      default:
        return 0
    }
  })

  return (
    <div className="min-h-screen bg-background">
      <Header />

      <div className="container mx-auto px-4 py-6">
        {/* Breadcrumb */}
        <nav className="flex items-center space-x-2 text-sm mb-6">
          <Link href="/" className="text-blue-600 hover:underline">
            Home
          </Link>
          <span className="text-muted-foreground">›</span>
          <span className="text-muted-foreground">Search Car Accident Lawyers</span>
        </nav>

        {/* Results header */}
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-8">
          <div>
            <h1 className="text-2xl font-semibold text-muted-foreground mb-2">
              <span className="font-bold text-foreground">{sortedLawyers.length}</span> matches found
            </h1>
          </div>

          <div className="flex gap-4 mt-4 md:mt-0">
            <Select value={sortBy} onValueChange={setSortBy}>
              <SelectTrigger className="w-[140px]">
                <SelectValue placeholder="Sort By" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="rating">Rating</SelectItem>
                <SelectItem value="reviews">Reviews</SelectItem>
                <SelectItem value="name">Name</SelectItem>
              </SelectContent>
            </Select>

            <Select value={orderBy} onValueChange={setOrderBy}>
              <SelectTrigger className="w-[140px]">
                <SelectValue placeholder="Order" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="desc">Descending</SelectItem>
                <SelectItem value="asc">Ascending</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        {/* Results list */}
        <div className="space-y-6">
          {sortedLawyers.map((lawyer) => (
            <div
              key={lawyer.source_id}
              className="bg-white rounded-lg shadow-sm border p-6 hover:shadow-md transition-shadow"
            >
              <div className="flex gap-6">
                {/* Profile image */}
                <div className="flex-shrink-0">
                  <div className="relative w-32 h-32 rounded-lg overflow-hidden">
                    <Image
                      src={lawyer.image_url || "/placeholder.svg"}
                      alt={lawyer.title}
                      fill
                      className="object-cover"
                    />
                    <div className="absolute top-2 left-2">
                      <Badge variant="destructive" className="text-xs px-2 py-1">
                        ⚡
                      </Badge>
                    </div>
                  </div>
                </div>

                {/* Content */}
                <div className="flex-1 min-w-0">
                  <div className="flex items-start justify-between mb-2">
                    <div>
                      <div className="flex items-center gap-2 mb-1">
                        <h2 className="text-xl font-semibold text-blue-600 hover:underline cursor-pointer">
                          <Link href={`/lawyer/${lawyer.source_id}`}>{lawyer.title}</Link>
                        </h2>
                        <Badge variant="outline" className="text-xs">
                          +{Math.floor(Math.random() * 20) + 5}
                        </Badge>
                      </div>
                      <p className="text-muted-foreground mb-2">{lawyer.credentials}</p>

                      <div className="flex items-center gap-4 mb-3">
                        <div className="flex items-center gap-1">{renderStars(lawyer.total_score)}</div>
                        <span className="text-muted-foreground">{lawyer.reviews_count} Feedback</span>
                      </div>
                    </div>

                    <Button variant="ghost" size="sm">
                      <Heart className="h-4 w-4" />
                    </Button>
                  </div>

                  {/* Service keywords */}
                  <div className="flex flex-wrap gap-2 mb-4">
                    {lawyer.processed_reviews.service_keywords.slice(0, 6).map((keyword, index) => (
                      <Badge key={index} variant="secondary" className="text-xs bg-gray-100 text-gray-700">
                        {keyword}
                      </Badge>
                    ))}
                    <Button variant="ghost" size="sm" className="text-blue-600 text-xs p-0 h-auto">
                      View all
                    </Button>
                  </div>
                </div>

                {/* Right sidebar */}
                <div className="flex-shrink-0 w-64 space-y-3">
                  <div className="flex items-center text-muted-foreground">
                    <MapPin className="h-4 w-4 mr-2" />
                    <span className="text-sm">
                      {lawyer.city}, {lawyer.state}
                    </span>
                  </div>

                  <div className="flex items-center text-muted-foreground">
                    <Clock className="h-4 w-4 mr-2" />
                    <span className="text-sm">Mon, Tue, Wed, Thu, Fri</span>
                  </div>

                  <div className="flex items-center text-muted-foreground">
                    <span className="text-sm">👥 Lawyers Onboard: 11</span>
                  </div>

                  <div className="flex items-center text-muted-foreground">
                    <span className="text-sm">⏰ 24/7 available</span>
                  </div>

                  <div className="flex items-center text-green-600 font-medium">
                    <span className="text-sm">{lawyer.availability}</span>
                  </div>

                  <div className="flex items-center text-muted-foreground">
                    <span className="text-sm">{lawyer.pricing}</span>
                  </div>

                  <Button className="w-full" size="sm">
                    View More
                  </Button>

                  <Button variant="outline" className="w-full bg-transparent" size="sm">
                    View Full Profile
                  </Button>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      <Footer />
    </div>
  )
}
